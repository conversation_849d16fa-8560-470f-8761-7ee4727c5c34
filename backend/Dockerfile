FROM ubuntu:20.04

# set timezone
ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Update repositories to old releases
RUN sed -i -e 's/:\/\/(archive.ubuntu.com\|security.ubuntu.com)/old-releases.ubuntu.com/g' /etc/apt/sources.list
RUN apt-get update

# Install essential packages
RUN apt-get install -y software-properties-common curl locales build-essential libatlas-base-dev libopenblas-dev liblapack-dev gfortran

# Install Python 3.11 and dependencies
RUN add-apt-repository -y ppa:deadsnakes/ppa
RUN apt-get update
RUN apt-get install -y python3.11 python3.11-dev libpq-dev

# Install distutils (needed for pip installation)
RUN apt-get install -y python3.11-distutils

# Install pip using get-pip.py
RUN curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
RUN python3.11 get-pip.py

# Upgrade pip and install necessary Python packages
RUN python3.11 -m pip install --upgrade pip setuptools distlib gunicorn

# Install all locales and generate en_IN.UTF-8
RUN locale-gen en_IN.UTF-8

# Set environment variables
ENV LANG en_IN
ENV LANGUAGE en_IN:en
ENV LC_LANG en_IN.UTF-8
ENV LC_ALL en_IN
ENV ACCEPT_EULA Y
ENV ENVIRON=STAGING
ENV SERVER_TYPE=STAGING
ENV WORKSPACE_PATH=./backend
ENV WORKSPACE_DATA_PATH=./backend/backend_data
ENV PPT_DATA_PATH=./backend/misc
ENV DEBUG=False
ENV APP_MAIN_PORT=80
ENV GRAPH_CACHE_TIMEOUT=300
ENV SHARE_GRAPH_CACHE_TIMEOUT=1800
ENV REDIS_HOST=REDIS_SERVER
ENV REDIS_PORT=6379
ENV AWS_ACCESS_KEY_ID=AWS_S3_ACCESS_KEY
ENV AWS_SECRET_ACCESS_KEY=AWS_S3_SECRET_KEY
ENV BUCKET_NAME=awa-reports-staging
ENV REGION_NAME=ap-south-1
ENV QUERY_TIMEOUT=60
ENV AI_INSIGHTS_LINK=http://************:2270
ENV AI_DEEPER_INSIGHT_LINK=http://************:2270
ENV DD_HOST=**********
ENV CELERY_ENABLE=True
ENV CELERY_APP_NAME=scheduler_celery
ENV REDIS_BACKEND_DB_ID=8
ENV REDIS_BROKER_DB_ID=8
ENV REPORT_SCHEDULER_INTERVAL=60.0
ENV CELERY_GENERATED_QUEUE_NAME=generate_now_queue
ENV CELERY_GENERATED_ROUTE_NAME=generate_now_task_key
ENV CELERY_SCHEDULED_QUEUE_NAME=scheduled_queue
ENV CELERY_SCHEDULED_ROUTE_NAME=scheduling_task_key
ENV CELERY_GENERATED_TASK_NAME=generated_now
ENV CELERY_SCHEDULED_TASK_NAME=scheduled_task
ENV TWTEST_APP_TOKEN=TWTESTAPPTOKEN
# Install ODBC drivers for Microsoft SQL Server
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
RUN curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list > /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update
RUN apt-get install -y unixodbc-dev msodbcsql17

# Set environment variables for mssql-tools
RUN echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc

# Create project structure
RUN mkdir -p backend/backend_data/ppt_temp
COPY backend/ backend/

# Install Python dependencies from requirements.txt
RUN python3.11 -m pip install --no-cache-dir -r backend/requirements.txt

# Start Gunicorn API workers
ENTRYPOINT ["gunicorn", "--timeout", "0", "backend.main:app", "-b", "0.0.0.0:80", "--workers", "5", "-k", "uvicorn.workers.UvicornWorker", "--access-logfile", "-"]
