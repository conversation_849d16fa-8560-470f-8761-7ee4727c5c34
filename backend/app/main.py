from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from websocket_server import websocket_router

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://*************:5173",
        "http://192.168.0.*:5173"  # Allow any device on your network
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add a simple test endpoint
@app.get("/test")
async def test():
    return {"message": "Server is running"}

app.include_router(websocket_router, prefix="/ws")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)