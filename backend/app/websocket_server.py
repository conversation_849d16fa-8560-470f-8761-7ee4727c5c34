from fastapi import <PERSON><PERSON>out<PERSON>, WebSocket, WebSocketDisconnect
from typing import Dict
import json

websocket_router = APIRouter()

# Store active connections
active_connections: Dict[str, WebSocket] = {}

@websocket_router.websocket("/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    print(f"WebSocket connection attempt for user: {user_id}")
    await websocket.accept()
    active_connections[user_id] = websocket
    print(f"User {user_id} connected successfully")
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            print(f"Received message from {user_id}: {message}")
            
            # Broadcast message to all connected clients
            for connection in active_connections.values():
                if connection != websocket:
                    await connection.send_text(data)
                    
    except WebSocketDisconnect:
        if user_id in active_connections:
            del active_connections[user_id]
        print(f"User {user_id} disconnected")
    except Exception as e:
        print(f"Error in WebSocket connection: {e}")
        if user_id in active_connections:
            del active_connections[user_id]