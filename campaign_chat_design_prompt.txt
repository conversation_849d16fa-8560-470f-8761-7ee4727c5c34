
🎯 Project Overview:

I’m building a campaign-based influencer collaboration platform where brands can create marketing campaigns, invite influencers/creators, negotiate deals, and track deliverables via a real-time chat interface.

👥 User Roles:
1. Brand
2. Influencer / Creator

🧩 Core Modules to Design:

1. 📦 Campaign Management (Brand-Side)
   - Brand Dashboard:
     - View all campaigns (with status: Draft, Active, Completed)
     - Click on a campaign to open:
       - Campaign title, description, timeline, deliverables
       - Influencer invite status breakdown (e.g., Invited, Onboarded, Declined)
   - Create/Edit Campaign:
     - Form to define:
       - Campaign title
       - Description
       - Start/end date
       - Budget
       - Deliverables (selectable)
       - Invite influencers from platform (multi-select)

2. 🙋 Influencer Response Management
   - Influencer Campaign View:
     - List of campaigns they’ve been invited to
     - View campaign details
     - Accept or decline invitation
     - If accepted, get redirected to chat window

3. 💬 Real-Time Campaign Chat (Per Brand–Influencer–Campaign Thread)
   - Thread title (e.g., "Campaign: Summer Launch 2025")
   - Header: Campaign info summary, budget, deadlines
   - Message timeline (styled chat bubbles)
     - Text messages
     - Proposals (with price card & CTA buttons: Accept / Negotiate)
     - System messages (e.g., “You are now onboarded”)
   - Left sidebar: List of influencers (for brands)
   - Bulk message option (<PERSON> can send one message to all influencers with the same status)
   - Right sidebar (optional): Influencer info, last seen, post history

4. 💰 Proposal Card Component (In Chat)
   - Brand can send a proposal:
     - Deliverables (e.g., 1 Story, 1 Reel)
     - Offer amount (e.g., ₹25,000)
     - Validity (e.g., Accept before July 20)
   - Influencer can:
     - Accept → updates system message
     - Decline → suggest counter in chat
   - Proposal appears like a card in the chat

5. 📤 Deliverables + Proof Submission Flow
   - Influencer:
     - After accepting proposal:
       - Upload required proof (e.g., screenshot, link to post)
       - Mark task as submitted
   - Brand:
     - View submitted proof in chat
     - Approve/reject with optional feedback
     - Once approved → system marks as "Completed", triggers payment flow

6. 🚥 Status Tracking & Bulk Messaging
   - Statuses:
     - Invited, Onboarded, Negotiating, In Progress, Submitted, Completed, Declined
   - Bulk Messaging:
     - Brand can select one or more statuses (e.g., all Onboarded)
     - Send a message to all influencers with that status in that campaign

7. 👤 User Presence & Delivery Indicators
   - Show online/offline status (green dot + last seen tooltip)
   - Each message has:
     - Sent ✅
     - Delivered ✅✅
     - Seen ✅✅ (filled icon)

📊 Backend & Database Architecture (for reference)
Let the design align with these entities:
- users (brand, influencer)
- campaigns
- campaign_participants
- chat_threads
- chat_messages
- proposals
- deliverables
- notifications
- user_presence

🎨 Design Goals:
- Clean, modern interface for negotiations
- Real-time WebSocket powered chat (look like WhatsApp/Slack hybrid)
- Smart handling of proposal workflows
- Easy tracking of multiple influencers per campaign
- Intuitive CTA buttons for all actions (invite, accept, upload, approve)
- Fully mobile-responsive (chat especially)

🧠 Bonus Features to Consider:
- Show campaign timeline as a vertical tracker
- Allow filtering influencer threads by status
- Show campaign-level analytics (e.g., X invited, Y onboarded, Z completed)
- Highlight pending proposals visually in the chat

🎯 Deliverables Expected:
- High-fidelity designs of:
  - Campaign creation flow (brand)
  - Influencer invitation flow
  - Real-time campaign chat (both roles)
  - Proposal interaction in chat
  - Proof submission and approval
  - User presence and message status indicators
  - Bulk messaging interface (for brand)
- Mobile + desktop views
- Components reusable (e.g., proposal card, user avatar with status)
