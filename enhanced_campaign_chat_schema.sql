-- ===========================================
-- ENHANCED CAMPAIGN CHAT SCHEMA
-- ===========================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===========================================
-- USERS TABLE (Brands and Influencers)
-- ===========================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('brand', 'influencer', 'admin')),
    email VARCHAR(150) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    profile_pic_url TEXT,
    bio TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_token TEXT,
    verification_expires_at TIMESTAMP,
    reset_password_token TEXT,
    reset_token_expires_at TIMESTAMP,
    last_seen_at TIMESTAMP,
    is_online BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- Add indexes for common queries
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email ON users(email);

-- ===========================================
-- BRAND PROFILES (Extended brand info)
-- ===========================================
CREATE TABLE brand_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_name VARCHAR(150) NOT NULL,
    website_url TEXT,
    industry VARCHAR(100),
    logo_url TEXT,
    company_size VARCHAR(50),
    founded_year INTEGER,
    headquarters_location VARCHAR(150),
    social_media JSONB, -- Store social media links
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    CONSTRAINT check_brand_role CHECK (EXISTS (SELECT 1 FROM users WHERE id = user_id AND role = 'brand'))
);

-- ===========================================
-- INFLUENCER PROFILES (Extended influencer info)
-- ===========================================
CREATE TABLE influencer_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    display_name VARCHAR(100),
    category VARCHAR(100),
    audience_size INTEGER,
    audience_demographics JSONB,
    platforms JSONB, -- Store platform names and follower counts
    content_types TEXT[],
    engagement_rate NUMERIC(5,2),
    average_likes INTEGER,
    average_comments INTEGER,
    portfolio_links TEXT[],
    rates JSONB, -- Store platform-specific rates
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    CONSTRAINT check_influencer_role CHECK (EXISTS (SELECT 1 FROM users WHERE id = user_id AND role = 'influencer'))
);

-- ===========================================
-- CAMPAIGNS TABLE (Created by Brand)
-- ===========================================
CREATE TABLE campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    brand_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('draft', 'active', 'completed', 'cancelled', 'paused')),
    budget NUMERIC(12,2),
    currency VARCHAR(10) DEFAULT 'USD',
    target_audience JSONB,
    campaign_goals TEXT[],
    campaign_hashtags TEXT[],
    campaign_brief_url TEXT,
    campaign_assets_url TEXT[],
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    CONSTRAINT check_end_after_start CHECK (end_date >= start_date),
    CONSTRAINT check_brand_role CHECK (EXISTS (SELECT 1 FROM users WHERE id = brand_id AND role = 'brand'))
);

-- Add indexes for common queries
CREATE INDEX idx_campaigns_brand_id ON campaigns(brand_id);
CREATE INDEX idx_campaigns_status ON campaigns(status);
CREATE INDEX idx_campaigns_dates ON campaigns(start_date, end_date);

-- ===========================================
-- CAMPAIGN DELIVERABLE TYPES
-- ===========================================
CREATE TABLE campaign_deliverable_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL,
    content_type VARCHAR(50) NOT NULL,
    description TEXT,
    quantity INTEGER DEFAULT 1,
    requirements TEXT,
    suggested_rate NUMERIC(12,2),
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- CAMPAIGN PARTICIPANTS TABLE
-- ===========================================
CREATE TABLE campaign_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    influencer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL CHECK (status IN ('invited', 'onboarded', 'negotiating', 'in_progress', 'submitted', 'completed', 'declined', 'removed')),
    invited_at TIMESTAMP DEFAULT now(),
    onboarded_at TIMESTAMP,
    completed_at TIMESTAMP,
    declined_reason TEXT,
    notes TEXT,
    custom_budget NUMERIC(12,2),
    last_status_change TIMESTAMP DEFAULT now(),
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    CONSTRAINT unique_campaign_influencer UNIQUE (campaign_id, influencer_id)
);

-- Add indexes for common queries
CREATE INDEX idx_participants_campaign_id ON campaign_participants(campaign_id);
CREATE INDEX idx_participants_influencer_id ON campaign_participants(influencer_id);
CREATE INDEX idx_participants_status ON campaign_participants(status);

-- ===========================================
-- CHAT THREADS TABLE (One thread per influencer-campaign)
-- ===========================================
CREATE TABLE chat_threads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    brand_id UUID NOT NULL REFERENCES users(id),
    influencer_id UUID NOT NULL REFERENCES users(id),
    participant_id UUID NOT NULL REFERENCES campaign_participants(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    last_message_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    CONSTRAINT unique_campaign_thread UNIQUE (campaign_id, influencer_id)
);

-- Add indexes for common queries
CREATE INDEX idx_chat_threads_campaign_id ON chat_threads(campaign_id);
CREATE INDEX idx_chat_threads_brand_id ON chat_threads(brand_id);
CREATE INDEX idx_chat_threads_influencer_id ON chat_threads(influencer_id);
CREATE INDEX idx_chat_threads_last_message ON chat_threads(last_message_at DESC);

-- ===========================================
-- CHAT MESSAGES TABLE
-- ===========================================
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    thread_id UUID NOT NULL REFERENCES chat_threads(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id),
    message TEXT,
    message_type VARCHAR(30) NOT NULL CHECK (message_type IN ('text', 'image', 'file', 'system', 'proposal', 'deliverable', 'bulk')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('sent', 'delivered', 'seen')) DEFAULT 'sent',
    metadata JSONB, -- For additional message data
    parent_message_id UUID REFERENCES chat_messages(id), -- For replies/threads
    is_pinned BOOLEAN DEFAULT FALSE,
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT now(),
    delivered_at TIMESTAMP,
    seen_at TIMESTAMP
);

-- Add indexes for common queries
CREATE INDEX idx_chat_messages_thread_id ON chat_messages(thread_id);
CREATE INDEX idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_chat_messages_parent ON chat_messages(parent_message_id);

-- ===========================================
-- PROPOSALS TABLE (Proposal messages)
-- ===========================================
CREATE TABLE proposals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
    thread_id UUID NOT NULL REFERENCES chat_threads(id),
    campaign_id UUID NOT NULL REFERENCES campaigns(id),
    participant_id UUID NOT NULL REFERENCES campaign_participants(id),
    proposed_by UUID NOT NULL REFERENCES users(id),
    deliverables JSONB NOT NULL, -- Array of deliverable items with details
    total_amount NUMERIC(12,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'accepted', 'declined', 'countered', 'expired')),
    valid_until TIMESTAMP,
    terms_conditions TEXT,
    payment_terms TEXT,
    version INTEGER DEFAULT 1, -- For tracking negotiation versions
    parent_proposal_id UUID REFERENCES proposals(id), -- For counter-proposals
    response_message TEXT, -- Reason for accept/decline/counter
    responded_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- Add indexes for common queries
CREATE INDEX idx_proposals_thread_id ON proposals(thread_id);
CREATE INDEX idx_proposals_campaign_id ON proposals(campaign_id);
CREATE INDEX idx_proposals_participant_id ON proposals(participant_id);
CREATE INDEX idx_proposals_status ON proposals(status);
CREATE INDEX idx_proposals_valid_until ON proposals(valid_until);

-- ===========================================
-- DELIVERABLES TABLE (Actual content posting)
-- ===========================================
CREATE TABLE deliverables (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    proposal_id UUID NOT NULL REFERENCES proposals(id),
    participant_id UUID NOT NULL REFERENCES campaign_participants(id),
    campaign_id UUID NOT NULL REFERENCES campaigns(id),
    type VARCHAR(50) NOT NULL,
    platform VARCHAR(50) NOT NULL,
    description TEXT,
    due_date DATE,
    content_url TEXT,
    proof_image_url TEXT[],
    proof_description TEXT,
    feedback TEXT,
    submitted_at TIMESTAMP,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP,
    rejected_at TIMESTAMP,
    rejection_reason TEXT,
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'submitted', 'approved', 'rejected', 'revision_requested')) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- Add indexes for common queries
CREATE INDEX idx_deliverables_proposal_id ON deliverables(proposal_id);
CREATE INDEX idx_deliverables_participant_id ON deliverables(participant_id);
CREATE INDEX idx_deliverables_campaign_id ON deliverables(campaign_id);
CREATE INDEX idx_deliverables_status ON deliverables(status);
CREATE INDEX idx_deliverables_due_date ON deliverables(due_date);

-- ===========================================
-- DELIVERABLE REVISIONS (Track revision history)
-- ===========================================
CREATE TABLE deliverable_revisions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    deliverable_id UUID NOT NULL REFERENCES deliverables(id) ON DELETE CASCADE,
    revision_number INTEGER NOT NULL,
    content_url TEXT,
    proof_image_url TEXT[],
    proof_description TEXT,
    feedback TEXT,
    submitted_at TIMESTAMP NOT NULL DEFAULT now(),
    status VARCHAR(20) NOT NULL CHECK (status IN ('submitted', 'approved', 'rejected', 'revision_requested')),
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- NOTIFICATIONS TABLE (Push/Badge)
-- ===========================================
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('message', 'proposal', 'deliverable', 'campaign_update', 'system', 'payment')),
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    related_entity_type VARCHAR(50), -- 'campaign', 'thread', 'proposal', 'deliverable'
    related_entity_id UUID,
    action_url TEXT,
    is_seen BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT now(),
    seen_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Add indexes for common queries
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_seen ON notifications(is_seen);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX idx_notifications_type ON notifications(type);

-- ===========================================
-- USER PRESENCE TABLE (Tracks user online/offline)
-- ===========================================
CREATE TABLE user_presence (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    is_online BOOLEAN DEFAULT FALSE,
    last_seen_at TIMESTAMP,
    socket_session_id TEXT, -- For tracking WebSocket connections
    device_info JSONB, -- Store device/browser info
    updated_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- TYPING INDICATORS (Real-time typing status)
-- ===========================================
CREATE TABLE typing_indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    thread_id UUID NOT NULL REFERENCES chat_threads(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_typing BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT now(),
    expires_at TIMESTAMP DEFAULT now() + INTERVAL '10 seconds',
    CONSTRAINT unique_thread_user_typing UNIQUE (thread_id, user_id)
);

-- ===========================================
-- BULK MESSAGES (For brand → multiple influencers)
-- ===========================================
CREATE TABLE bulk_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id),
    message TEXT NOT NULL,
    target_status VARCHAR(20)[], -- Array of statuses to target
    message_type VARCHAR(20) NOT NULL CHECK (message_type IN ('text', 'system', 'announcement')),
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- BULK MESSAGE RECIPIENTS
-- ===========================================
CREATE TABLE bulk_message_recipients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bulk_message_id UUID NOT NULL REFERENCES bulk_messages(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES users(id),
    thread_id UUID NOT NULL REFERENCES chat_threads(id),
    individual_message_id UUID REFERENCES chat_messages(id), -- Link to individual message
    status VARCHAR(20) NOT NULL CHECK (status IN ('sent', 'delivered', 'seen', 'failed')) DEFAULT 'sent',
    delivered_at TIMESTAMP,
    seen_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT now()
);

-- Add indexes for common queries
CREATE INDEX idx_bulk_recipients_bulk_id ON bulk_message_recipients(bulk_message_id);
CREATE INDEX idx_bulk_recipients_recipient ON bulk_message_recipients(recipient_id);
CREATE INDEX idx_bulk_recipients_thread ON bulk_message_recipients(thread_id);

-- ===========================================
-- CAMPAIGN ANALYTICS
-- ===========================================
CREATE TABLE campaign_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_invited INTEGER DEFAULT 0,
    total_onboarded INTEGER DEFAULT 0,
    total_negotiating INTEGER DEFAULT 0,
    total_in_progress INTEGER DEFAULT 0,
    total_submitted INTEGER DEFAULT 0,
    total_completed INTEGER DEFAULT 0,
    total_declined INTEGER DEFAULT 0,
    avg_response_time_minutes NUMERIC(10,2),
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    CONSTRAINT unique_campaign_date UNIQUE (campaign_id, date)
);

-- ===========================================
-- CAMPAIGN TIMELINE EVENTS
-- ===========================================
CREATE TABLE campaign_timeline_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    event_date TIMESTAMP NOT NULL,
    is_milestone BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- PAYMENT TRACKING
-- ===========================================
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id),
    proposal_id UUID NOT NULL REFERENCES proposals(id),
    participant_id UUID NOT NULL REFERENCES campaign_participants(id),
    amount NUMERIC(12,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'USD',
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    payment_date TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- USER ACTIVITY LOG
-- ===========================================
CREATE TABLE user_activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    activity_type VARCHAR(50) NOT NULL,
    description TEXT,
    entity_type VARCHAR(50), -- 'campaign', 'proposal', 'message', etc.
    entity_id UUID,
    ip_address VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- VIEWS FOR COMMON QUERIES
-- ===========================================

-- Active campaigns with participant counts
CREATE VIEW active_campaigns_summary AS
SELECT 
    c.id AS campaign_id,
    c.title,
    c.brand_id,
    c.start_date,
    c.end_date,
    c.status,
    COUNT(cp.id) AS total_participants,
    SUM(CASE WHEN cp.status = 'invited' THEN 1 ELSE 0 END) AS invited_count,
    SUM(CASE WHEN cp.status = 'onboarded' THEN 1 ELSE 0 END) AS onboarded_count,
    SUM(CASE WHEN cp.status = 'negotiating' THEN 1 ELSE 0 END) AS negotiating_count,
    SUM(CASE WHEN cp.status = 'in_progress' THEN 1 ELSE 0 END) AS in_progress_count,
    SUM(CASE WHEN cp.status = 'submitted' THEN 1 ELSE 0 END) AS submitted_count,
    SUM(CASE WHEN cp.status = 'completed' THEN 1 ELSE 0 END) AS completed_count,
    SUM(CASE WHEN cp.status = 'declined' THEN 1 ELSE 0 END) AS declined_count
FROM 
    campaigns c
LEFT JOIN 
    campaign_participants cp ON c.id = cp.campaign_id
WHERE 
    c.status IN ('active', 'draft')
GROUP BY 
    c.id, c.title, c.brand_id, c.start_date, c.end_date, c.status;

-- Recent chat activity
CREATE VIEW recent_chat_activity AS
SELECT 
    ct.id AS thread_id,
    ct.campaign_id,
    ct.brand_id,
    ct.influencer_id,
    cp.status AS participant_status,
    c.title AS campaign_title,
    u_brand.name AS brand_name,
    u_influencer.name AS influencer_name,
    cm.id AS last_message_id,
    cm.message AS last_message,
    cm.message_type AS last_message_type,
    cm.created_at AS last_message_time,
    cm.sender_id AS last_message_sender_id,
    CASE WHEN cm.sender_id = ct.brand_id THEN 'brand' ELSE 'influencer' END AS last_message_sender_role
FROM 
    chat_threads ct
JOIN 
    campaigns c ON ct.campaign_id = c.id
JOIN 
    users u_brand ON ct.brand_id = u_brand.id
JOIN 
    users u_influencer ON ct.influencer_id = u_influencer.id
JOIN 
    campaign_participants cp ON ct.participant_id = cp.id
LEFT JOIN LATERAL (
    SELECT id, message, message_type, created_at, sender_id
    FROM chat_messages
    WHERE thread_id = ct.id
    ORDER BY created_at DESC
    LIMIT 1
) cm ON true
WHERE 
    ct.is_active = true
ORDER BY 
    cm.created_at DESC NULLS LAST;

-- Pending proposals view
CREATE VIEW pending_proposals AS
SELECT 
    p.id AS proposal_id,
    p.thread_id,
    p.campaign_id,
    p.participant_id,
    c.title AS campaign_title,
    u_brand.id AS brand_id,
    u_brand.name AS brand_name,
    u_influencer.id AS influencer_id,
    u_influencer.name AS influencer_name,
    p.total_amount,
    p.currency,
    p.status,
    p.valid_until,
    p.created_at,
    CASE WHEN p.valid_until < NOW() THEN true ELSE false END AS is_expired
FROM 
    proposals p
JOIN 
    chat_threads ct ON p.thread_id = ct.id
JOIN 
    campaigns c ON p.campaign_id = c.id
JOIN 
    users u_brand ON ct.brand_id = u_brand.id
JOIN 
    users u_influencer ON ct.influencer_id = u_influencer.id
WHERE 
    p.status = 'pending'
    AND (p.valid_until IS NULL OR p.valid_until > NOW())
ORDER BY 
    p.created_at DESC;

-- Pending deliverables view
CREATE VIEW pending_deliverables AS
SELECT 
    d.id AS deliverable_id,
    d.campaign_id,
    c.title AS campaign_title,
    d.participant_id,
    cp.influencer_id,
    u.name AS influencer_name,
    d.type,
    d.platform,
    d.description,
    d.due_date,
    d.status,
    CASE WHEN d.due_date < CURRENT_DATE THEN true ELSE false END AS is_overdue
FROM 
    deliverables d
JOIN 
    campaigns c ON d.campaign_id = c.id
JOIN 
    campaign_participants cp ON d.participant_id = cp.id
JOIN 
    users u ON cp.influencer_id = u.id
WHERE 
    d.status IN ('pending', 'submitted')
ORDER BY 
    d.due_date ASC;

-- Add triggers for automatic updates
CREATE OR REPLACE FUNCTION update_thread_last_message_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE chat_threads
    SET last_message_at = NEW.created_at,
        updated_at = NOW()
    WHERE id = NEW.thread_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_thread_timestamp
AFTER INSERT ON chat_messages
FOR EACH ROW
EXECUTE FUNCTION update_thread_last_message_timestamp();

-- Trigger to update campaign participant status when proposal is accepted
CREATE OR REPLACE FUNCTION update_participant_status_on_proposal_change()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'accepted' AND OLD.status = 'pending' THEN
        UPDATE campaign_participants
        SET status = 'in_progress',
            last_status_change = NOW(),
            updated_at = NOW()
        WHERE id = NEW.participant_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_participant_on_proposal_accept
AFTER UPDATE ON proposals
FOR EACH ROW
WHEN (NEW.status = 'accepted' AND OLD.status = 'pending')
EXECUTE FUNCTION update_participant_status_on_proposal_change();
