-- ===========================================
-- ENHANCED CHAT SCHEMA - INTEGRATED WITH EXISTING STRUCTURE
-- ===========================================

-- Create chat schema for chat-related tables
CREATE SCHEMA IF NOT EXISTS chat;
CREATE SCHEMA IF NOT EXISTS notifications;

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- BRAND USER ACCESS (Multiple users can access multiple brands)
-- ===========================================
CREATE TABLE chat.brand_user_access (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    user_id UUID NOT NULL,
    brand_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    access_level VARCHAR(20) DEFAULT 'member',
    can_create_campaigns BOOLEAN DEFAULT FALSE,
    can_manage_chat BOOLEAN DEFAULT TRUE,
    can_send_proposals BOOLEAN DEFAULT FALSE,
    can_approve_deliverables BOOLEAN DEFAULT FALSE,
    granted_by UUID,
    granted_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT brand_user_access_pkey PRIMARY KEY (id),
    CONSTRAINT unique_user_brand_access UNIQUE (user_id, brand_id),
    CONSTRAINT brand_user_access_level_check CHECK (access_level IN ('admin', 'manager', 'member', 'viewer')),
    CONSTRAINT brand_user_access_user_fkey FOREIGN KEY (user_id) REFERENCES users.users(id) ON DELETE CASCADE,
    CONSTRAINT brand_user_access_brand_fkey FOREIGN KEY (brand_id) REFERENCES users.brands(id) ON DELETE CASCADE,
    CONSTRAINT brand_user_access_organization_fkey FOREIGN KEY (organization_id) REFERENCES users.organizations(id) ON DELETE CASCADE
);

-- Indexes for brand user access
CREATE INDEX idx_brand_user_access_user_id ON chat.brand_user_access(user_id);
CREATE INDEX idx_brand_user_access_brand_id ON chat.brand_user_access(brand_id);
CREATE INDEX idx_brand_user_access_active ON chat.brand_user_access(is_active) WHERE is_active = TRUE;

-- ===========================================
-- CHAT THREADS TABLE (One thread per influencer-campaign)
-- ===========================================
CREATE TABLE chat.threads (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    campaign_id UUID NOT NULL,
    brand_id UUID NOT NULL, -- The brand entity (not user)
    influencer_user_id UUID NOT NULL,
    campaign_influencer_id UUID NOT NULL, -- Links to campaigns.campaign_influencers
    thread_title TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_message_at TIMESTAMPTZ,
    unread_count_brand INTEGER DEFAULT 0,
    unread_count_influencer INTEGER DEFAULT 0,
    is_archived BOOLEAN DEFAULT FALSE,
    archived_by UUID,
    archived_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT threads_pkey PRIMARY KEY (id),
    CONSTRAINT unique_campaign_influencer_thread UNIQUE (campaign_id, influencer_user_id),
    CONSTRAINT threads_campaign_id_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns.campaigns(id) ON DELETE CASCADE,
    CONSTRAINT threads_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES users.brands(id) ON DELETE CASCADE,
    CONSTRAINT threads_campaign_influencer_fkey FOREIGN KEY (campaign_influencer_id) REFERENCES campaigns.campaign_influencers(id) ON DELETE CASCADE
);

-- Indexes for chat threads
CREATE INDEX idx_threads_campaign_id ON chat.threads(campaign_id);
CREATE INDEX idx_threads_brand_id ON chat.threads(brand_id);
CREATE INDEX idx_threads_influencer_user ON chat.threads(influencer_user_id);
CREATE INDEX idx_threads_last_message ON chat.threads(last_message_at DESC);
CREATE INDEX idx_threads_active ON chat.threads(is_active) WHERE is_active = TRUE;

-- ===========================================
-- CHAT MESSAGES TABLE
-- ===========================================
CREATE TABLE chat.messages (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    thread_id UUID NOT NULL,
    sender_user_id UUID NOT NULL, -- The actual user who sent the message
    sender_brand_id UUID, -- The brand on behalf of which the message was sent (NULL for influencer messages)
    message_text TEXT,
    message_type VARCHAR(30) NOT NULL DEFAULT 'text',
    status VARCHAR(20) NOT NULL DEFAULT 'sent',
    metadata JSONB DEFAULT '{}',
    parent_message_id UUID, -- For replies/threads
    is_pinned BOOLEAN DEFAULT FALSE,
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMPTZ,
    file_attachments JSONB DEFAULT '[]', -- Array of file objects
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMPTZ,
    seen_at TIMESTAMPTZ,

    CONSTRAINT messages_pkey PRIMARY KEY (id),
    CONSTRAINT messages_message_type_check CHECK (message_type IN ('text', 'image', 'file', 'system', 'proposal', 'deliverable', 'bulk', 'announcement')),
    CONSTRAINT messages_status_check CHECK (status IN ('sent', 'delivered', 'seen', 'failed')),
    CONSTRAINT messages_thread_id_fkey FOREIGN KEY (thread_id) REFERENCES chat.threads(id) ON DELETE CASCADE,
    CONSTRAINT messages_sender_user_fkey FOREIGN KEY (sender_user_id) REFERENCES users.users(id),
    CONSTRAINT messages_sender_brand_fkey FOREIGN KEY (sender_brand_id) REFERENCES users.brands(id),
    CONSTRAINT messages_parent_fkey FOREIGN KEY (parent_message_id) REFERENCES chat.messages(id)
);

-- Indexes for chat messages
CREATE INDEX idx_messages_thread_id ON chat.messages(thread_id);
CREATE INDEX idx_messages_sender_user ON chat.messages(sender_user_id);
CREATE INDEX idx_messages_sender_brand ON chat.messages(sender_brand_id);
CREATE INDEX idx_messages_created_at ON chat.messages(created_at);
CREATE INDEX idx_messages_parent ON chat.messages(parent_message_id);
CREATE INDEX idx_messages_type ON chat.messages(message_type);
CREATE INDEX idx_messages_thread_created ON chat.messages(thread_id, created_at DESC);

-- ===========================================
-- PROPOSALS TABLE (Enhanced proposal system)
-- ===========================================
CREATE TABLE chat.proposals (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    message_id UUID NOT NULL,
    thread_id UUID NOT NULL,
    campaign_id UUID NOT NULL,
    campaign_influencer_id UUID NOT NULL,
    proposed_by_user_id UUID NOT NULL,
    proposal_type VARCHAR(20) DEFAULT 'initial', -- initial, counter, revision
    deliverables JSONB NOT NULL, -- Array of deliverable items
    total_amount NUMERIC(12,2) NOT NULL,
    currency_id UUID,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    valid_until TIMESTAMPTZ,
    terms_conditions TEXT,
    payment_terms TEXT,
    milestone_payments JSONB DEFAULT '[]', -- Array of payment milestones
    version INTEGER DEFAULT 1,
    parent_proposal_id UUID, -- For counter-proposals
    response_message TEXT,
    responded_at TIMESTAMPTZ,
    auto_expire_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT proposals_pkey PRIMARY KEY (id),
    CONSTRAINT proposals_proposal_type_check CHECK (proposal_type IN ('initial', 'counter', 'revision')),
    CONSTRAINT proposals_status_check CHECK (status IN ('pending', 'accepted', 'declined', 'countered', 'expired', 'withdrawn')),
    CONSTRAINT proposals_message_id_fkey FOREIGN KEY (message_id) REFERENCES chat.messages(id) ON DELETE CASCADE,
    CONSTRAINT proposals_thread_id_fkey FOREIGN KEY (thread_id) REFERENCES chat.threads(id),
    CONSTRAINT proposals_campaign_id_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns.campaigns(id),
    CONSTRAINT proposals_campaign_influencer_fkey FOREIGN KEY (campaign_influencer_id) REFERENCES campaigns.campaign_influencers(id),
    CONSTRAINT proposals_currency_fkey FOREIGN KEY (currency_id) REFERENCES campaigns.master_currencies(id),
    CONSTRAINT proposals_parent_fkey FOREIGN KEY (parent_proposal_id) REFERENCES chat.proposals(id)
);

-- Indexes for proposals
CREATE INDEX idx_proposals_thread_id ON chat.proposals(thread_id);
CREATE INDEX idx_proposals_campaign_id ON chat.proposals(campaign_id);
CREATE INDEX idx_proposals_status ON chat.proposals(status);
CREATE INDEX idx_proposals_valid_until ON chat.proposals(valid_until);
CREATE INDEX idx_proposals_parent ON chat.proposals(parent_proposal_id);

-- ===========================================
-- DELIVERABLES TABLE (Content submission tracking)
-- ===========================================
CREATE TABLE chat.deliverables (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    proposal_id UUID NOT NULL,
    campaign_influencer_id UUID NOT NULL,
    campaign_id UUID NOT NULL,
    deliverable_type VARCHAR(50) NOT NULL,
    platform VARCHAR(50) NOT NULL,
    description TEXT,
    requirements TEXT,
    due_date DATE,
    content_url TEXT,
    proof_files JSONB DEFAULT '[]', -- Array of proof file objects
    proof_description TEXT,
    submission_notes TEXT,
    feedback TEXT,
    submitted_at TIMESTAMPTZ,
    reviewed_by UUID,
    reviewed_at TIMESTAMPTZ,
    approved_at TIMESTAMPTZ,
    rejected_at TIMESTAMPTZ,
    rejection_reason TEXT,
    revision_requested_at TIMESTAMPTZ,
    revision_notes TEXT,
    status VARCHAR(30) NOT NULL DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'normal',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT deliverables_pkey PRIMARY KEY (id),
    CONSTRAINT deliverables_status_check CHECK (status IN ('pending', 'in_progress', 'submitted', 'under_review', 'approved', 'rejected', 'revision_requested', 'completed')),
    CONSTRAINT deliverables_priority_check CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    CONSTRAINT deliverables_proposal_fkey FOREIGN KEY (proposal_id) REFERENCES chat.proposals(id),
    CONSTRAINT deliverables_campaign_influencer_fkey FOREIGN KEY (campaign_influencer_id) REFERENCES campaigns.campaign_influencers(id),
    CONSTRAINT deliverables_campaign_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns.campaigns(id)
);

-- Indexes for deliverables
CREATE INDEX idx_deliverables_proposal_id ON chat.deliverables(proposal_id);
CREATE INDEX idx_deliverables_campaign_influencer ON chat.deliverables(campaign_influencer_id);
CREATE INDEX idx_deliverables_campaign_id ON chat.deliverables(campaign_id);
CREATE INDEX idx_deliverables_status ON chat.deliverables(status);
CREATE INDEX idx_deliverables_due_date ON chat.deliverables(due_date);
CREATE INDEX idx_deliverables_platform ON chat.deliverables(platform);

-- ===========================================
-- DELIVERABLE REVISIONS (Track revision history)
-- ===========================================
CREATE TABLE chat.deliverable_revisions (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    deliverable_id UUID NOT NULL,
    revision_number INTEGER NOT NULL,
    content_url TEXT,
    proof_files JSONB DEFAULT '[]',
    proof_description TEXT,
    submission_notes TEXT,
    feedback TEXT,
    submitted_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMPTZ,
    status VARCHAR(30) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT deliverable_revisions_pkey PRIMARY KEY (id),
    CONSTRAINT deliverable_revisions_status_check CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected', 'revision_requested')),
    CONSTRAINT deliverable_revisions_deliverable_fkey FOREIGN KEY (deliverable_id) REFERENCES chat.deliverables(id) ON DELETE CASCADE,
    CONSTRAINT unique_deliverable_revision UNIQUE (deliverable_id, revision_number)
);

-- ===========================================
-- USER PRESENCE TABLE (Real-time status)
-- ===========================================
CREATE TABLE chat.user_presence (
    user_id UUID PRIMARY KEY,
    is_online BOOLEAN DEFAULT FALSE,
    last_seen_at TIMESTAMPTZ,
    socket_session_id TEXT,
    device_info JSONB DEFAULT '{}',
    current_thread_id UUID,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT user_presence_thread_fkey FOREIGN KEY (current_thread_id) REFERENCES chat.threads(id)
);

-- ===========================================
-- TYPING INDICATORS (Real-time typing status)
-- ===========================================
CREATE TABLE chat.typing_indicators (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    thread_id UUID NOT NULL,
    user_id UUID NOT NULL,
    is_typing BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP + INTERVAL '10 seconds',
    
    CONSTRAINT typing_indicators_pkey PRIMARY KEY (id),
    CONSTRAINT unique_thread_user_typing UNIQUE (thread_id, user_id),
    CONSTRAINT typing_indicators_thread_fkey FOREIGN KEY (thread_id) REFERENCES chat.threads(id) ON DELETE CASCADE
);

-- ===========================================
-- BULK MESSAGES (Brand to multiple influencers)
-- ===========================================
CREATE TABLE chat.bulk_messages (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    campaign_id UUID NOT NULL,
    sender_user_id UUID NOT NULL,
    message_text TEXT NOT NULL,
    target_statuses TEXT[] NOT NULL, -- Array of campaign_influencer statuses
    message_type VARCHAR(20) NOT NULL DEFAULT 'text',
    total_recipients INTEGER DEFAULT 0,
    successful_sends INTEGER DEFAULT 0,
    failed_sends INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT bulk_messages_pkey PRIMARY KEY (id),
    CONSTRAINT bulk_messages_message_type_check CHECK (message_type IN ('text', 'system', 'announcement')),
    CONSTRAINT bulk_messages_campaign_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns.campaigns(id) ON DELETE CASCADE,
    CONSTRAINT bulk_messages_sender_fkey FOREIGN KEY (sender_user_id) REFERENCES users.users(id)
);

-- ===========================================
-- BULK MESSAGE RECIPIENTS (Track individual sends)
-- ===========================================
CREATE TABLE chat.bulk_message_recipients (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    bulk_message_id UUID NOT NULL,
    recipient_user_id UUID NOT NULL,
    thread_id UUID NOT NULL,
    individual_message_id UUID,
    status VARCHAR(20) NOT NULL DEFAULT 'sent',
    delivered_at TIMESTAMPTZ,
    seen_at TIMESTAMPTZ,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT bulk_message_recipients_pkey PRIMARY KEY (id),
    CONSTRAINT bulk_recipients_status_check CHECK (status IN ('sent', 'delivered', 'seen', 'failed')),
    CONSTRAINT bulk_recipients_bulk_fkey FOREIGN KEY (bulk_message_id) REFERENCES chat.bulk_messages(id) ON DELETE CASCADE,
    CONSTRAINT bulk_recipients_thread_fkey FOREIGN KEY (thread_id) REFERENCES chat.threads(id),
    CONSTRAINT bulk_recipients_message_fkey FOREIGN KEY (individual_message_id) REFERENCES chat.messages(id)
);

-- ===========================================
-- NOTIFICATIONS SCHEMA TABLES
-- ===========================================

-- Enhanced notifications table
CREATE TABLE notifications.notifications (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    user_id UUID NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal',
    category VARCHAR(50) DEFAULT 'general',
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    action_url TEXT,
    action_data JSONB DEFAULT '{}',
    is_seen BOOLEAN DEFAULT FALSE,
    is_read BOOLEAN DEFAULT FALSE,
    is_actionable BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    seen_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ,
    
    CONSTRAINT notifications_pkey PRIMARY KEY (id),
    CONSTRAINT notifications_type_check CHECK (type IN ('message', 'proposal', 'deliverable', 'campaign_update', 'system', 'payment', 'deadline', 'reminder')),
    CONSTRAINT notifications_priority_check CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    CONSTRAINT notifications_category_check CHECK (category IN ('general', 'chat', 'campaign', 'proposal', 'deliverable', 'payment', 'system'))
);

-- Indexes for notifications
CREATE INDEX idx_notifications_user_id ON notifications.notifications(user_id);
CREATE INDEX idx_notifications_is_seen ON notifications.notifications(is_seen);
CREATE INDEX idx_notifications_created_at ON notifications.notifications(created_at DESC);
CREATE INDEX idx_notifications_type ON notifications.notifications(type);
CREATE INDEX idx_notifications_priority ON notifications.notifications(priority);

-- ===========================================
-- USER ACTIVITY LOG (Moved to chat schema)
-- ===========================================
CREATE TABLE chat.user_activity_log (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    user_id UUID NOT NULL,
    brand_id UUID,
    activity_type VARCHAR(50) NOT NULL,
    description TEXT,
    entity_type VARCHAR(50),
    entity_id UUID,
    metadata JSONB DEFAULT '{}',
    ip_address VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT user_activity_log_pkey PRIMARY KEY (id),
    CONSTRAINT user_activity_log_user_fkey FOREIGN KEY (user_id) REFERENCES users.users(id),
    CONSTRAINT user_activity_log_brand_fkey FOREIGN KEY (brand_id) REFERENCES users.brands(id)
);

-- Indexes for user activity log
CREATE INDEX idx_user_activity_user_id ON chat.user_activity_log(user_id);
CREATE INDEX idx_user_activity_brand_id ON chat.user_activity_log(brand_id);
CREATE INDEX idx_user_activity_type ON chat.user_activity_log(activity_type);
CREATE INDEX idx_user_activity_created_at ON chat.user_activity_log(created_at);

-- ===========================================
-- PAYMENT TRACKING (Integrates with existing structure)
-- ===========================================
CREATE TABLE chat.payments (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    campaign_id UUID NOT NULL,
    proposal_id UUID NOT NULL,
    campaign_influencer_id UUID NOT NULL,
    amount NUMERIC(12,2) NOT NULL,
    currency_id UUID,
    payment_type VARCHAR(50) DEFAULT 'full',
    milestone_number INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    payment_gateway_response JSONB DEFAULT '{}',
    scheduled_date DATE,
    processed_at TIMESTAMPTZ,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT payments_pkey PRIMARY KEY (id),
    CONSTRAINT payments_payment_type_check CHECK (payment_type IN ('full', 'milestone', 'advance', 'bonus')),
    CONSTRAINT payments_status_check CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
    CONSTRAINT payments_campaign_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns.campaigns(id),
    CONSTRAINT payments_proposal_fkey FOREIGN KEY (proposal_id) REFERENCES chat.proposals(id),
    CONSTRAINT payments_campaign_influencer_fkey FOREIGN KEY (campaign_influencer_id) REFERENCES campaigns.campaign_influencers(id),
    CONSTRAINT payments_currency_fkey FOREIGN KEY (currency_id) REFERENCES campaigns.master_currencies(id)
);

-- ===========================================
-- USEFUL VIEWS FOR COMMON QUERIES
-- ===========================================

-- Active campaign threads with latest message info
CREATE VIEW chat.active_threads_summary AS
SELECT
    t.id AS thread_id,
    t.campaign_id,
    c.name AS campaign_name,
    c.status AS campaign_status,
    t.brand_id,
    b.name AS brand_name,
    t.influencer_user_id,
    u_inf.name AS influencer_name,
    ci.status AS influencer_status,
    t.last_message_at,
    t.unread_count_brand,
    t.unread_count_influencer,
    m.message_text AS last_message,
    m.message_type AS last_message_type,
    m.sender_user_id AS last_message_sender_user_id,
    u_sender.name AS last_message_sender_name,
    m.sender_brand_id AS last_message_sender_brand_id,
    CASE
        WHEN m.sender_brand_id IS NOT NULL THEN 'brand'
        ELSE 'influencer'
    END AS last_message_sender_role
FROM
    chat.threads t
JOIN
    campaigns.campaigns c ON t.campaign_id = c.id
JOIN
    users.brands b ON t.brand_id = b.id
JOIN
    users.users u_inf ON t.influencer_user_id = u_inf.id
JOIN
    campaigns.campaign_influencers ci ON t.campaign_influencer_id = ci.id
LEFT JOIN LATERAL (
    SELECT message_text, message_type, sender_user_id, sender_brand_id, created_at
    FROM chat.messages
    WHERE thread_id = t.id
    ORDER BY created_at DESC
    LIMIT 1
) m ON true
LEFT JOIN
    users.users u_sender ON m.sender_user_id = u_sender.id
WHERE
    t.is_active = TRUE
    AND c.is_delete = FALSE
ORDER BY
    t.last_message_at DESC NULLS LAST;

-- Pending proposals summary
CREATE VIEW chat.pending_proposals_summary AS
SELECT 
    p.id AS proposal_id,
    p.thread_id,
    p.campaign_id,
    c.name AS campaign_name,
    p.campaign_influencer_id,
    ci.influencer_user_id,
    p.total_amount,
    curr.code AS currency_code,
    p.status,
    p.valid_until,
    p.created_at,
    CASE WHEN p.valid_until < NOW() THEN true ELSE false END AS is_expired,
    p.proposal_type,
    p.version
FROM 
    chat.proposals p
JOIN 
    campaigns.campaigns c ON p.campaign_id = c.id
JOIN 
    campaigns.campaign_influencers ci ON p.campaign_influencer_id = ci.id
LEFT JOIN 
    campaigns.master_currencies curr ON p.currency_id = curr.id
WHERE 
    p.status = 'pending'
    AND (p.valid_until IS NULL OR p.valid_until > NOW())
    AND c.is_delete = FALSE
ORDER BY 
    p.created_at DESC;

-- Pending deliverables summary
CREATE VIEW chat.pending_deliverables_summary AS
SELECT 
    d.id AS deliverable_id,
    d.campaign_id,
    c.name AS campaign_name,
    d.campaign_influencer_id,
    ci.influencer_user_id,
    d.deliverable_type,
    d.platform,
    d.description,
    d.due_date,
    d.status,
    d.priority,
    CASE WHEN d.due_date < CURRENT_DATE THEN true ELSE false END AS is_overdue,
    d.created_at,
    d.submitted_at
FROM 
    chat.deliverables d
JOIN 
    campaigns.campaigns c ON d.campaign_id = c.id
JOIN 
    campaigns.campaign_influencers ci ON d.campaign_influencer_id = ci.id
WHERE 
    d.status IN ('pending', 'in_progress', 'submitted', 'under_review', 'revision_requested')
    AND c.is_delete = FALSE
ORDER BY
    d.due_date ASC NULLS LAST;

-- ===========================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ===========================================

-- Update thread timestamp when new message is added
CREATE OR REPLACE FUNCTION update_thread_last_message_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE chat.threads
    SET last_message_at = NEW.created_at,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.thread_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_thread_timestamp
AFTER INSERT ON chat.messages
FOR EACH ROW
EXECUTE FUNCTION update_thread_last_message_timestamp();

-- Update campaign influencer status when proposal is accepted
CREATE OR REPLACE FUNCTION update_campaign_influencer_on_proposal_change()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'accepted' AND OLD.status = 'pending' THEN
        UPDATE campaigns.campaign_influencers
        SET status = 'ACCEPTED',
            accepted_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP,
            final_rates = NEW.deliverables,
            agreed_deliverables = NEW.deliverables
        WHERE id = NEW.campaign_influencer_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_campaign_influencer_on_proposal_accept
AFTER UPDATE ON chat.proposals
FOR EACH ROW
WHEN (NEW.status = 'accepted' AND OLD.status = 'pending')
EXECUTE FUNCTION update_campaign_influencer_on_proposal_change();

-- Update unread counts
CREATE OR REPLACE FUNCTION update_unread_counts()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.sender_brand_id IS NOT NULL THEN
        -- Message from brand user, increment influencer unread count
        UPDATE chat.threads
        SET unread_count_influencer = unread_count_influencer + 1
        WHERE id = NEW.thread_id;
    ELSE
        -- Message from influencer, increment brand unread count
        UPDATE chat.threads
        SET unread_count_brand = unread_count_brand + 1
        WHERE id = NEW.thread_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_unread_counts_trigger
AFTER INSERT ON chat.messages
FOR EACH ROW
EXECUTE FUNCTION update_unread_counts();

-- Add triggers for updated_at columns
CREATE TRIGGER update_threads_updated_at
BEFORE UPDATE ON chat.threads
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_proposals_updated_at
BEFORE UPDATE ON chat.proposals
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deliverables_updated_at
BEFORE UPDATE ON chat.deliverables
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at
BEFORE UPDATE ON chat.payments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Removed analytics schema trigger

-- ===========================================
-- ADDITIONAL HELPER FUNCTIONS
-- ===========================================

-- Function to mark messages as seen and reset unread count
CREATE OR REPLACE FUNCTION mark_messages_as_seen(
    p_thread_id UUID,
    p_user_id UUID
)
RETURNS INTEGER AS $$
DECLARE
    messages_updated INTEGER;
    is_brand_user BOOLEAN;
    v_brand_id UUID;
BEGIN
    -- Get the brand ID for this thread
    SELECT brand_id INTO v_brand_id
    FROM chat.threads
    WHERE id = p_thread_id;

    -- Check if user has access to this brand
    SELECT EXISTS (
        SELECT 1 FROM chat.brand_user_access
        WHERE user_id = p_user_id
        AND brand_id = v_brand_id
        AND is_active = TRUE
    ) INTO is_brand_user;

    -- Update messages as seen
    UPDATE chat.messages
    SET status = 'seen',
        seen_at = CURRENT_TIMESTAMP
    WHERE thread_id = p_thread_id
        AND (
            -- If brand user, mark influencer messages as seen
            (is_brand_user AND sender_brand_id IS NULL)
            OR
            -- If influencer, mark brand messages as seen
            (NOT is_brand_user AND sender_user_id != p_user_id)
        )
        AND status != 'seen';

    GET DIAGNOSTICS messages_updated = ROW_COUNT;

    -- Reset unread count for the user
    IF is_brand_user THEN
        UPDATE chat.threads
        SET unread_count_brand = 0
        WHERE id = p_thread_id;
    ELSE
        UPDATE chat.threads
        SET unread_count_influencer = 0
        WHERE id = p_thread_id;
    END IF;

    RETURN messages_updated;
END;
$$ LANGUAGE plpgsql;

-- Function to get campaign statistics
CREATE OR REPLACE FUNCTION get_campaign_chat_stats(p_campaign_id UUID)
RETURNS TABLE (
    total_threads INTEGER,
    active_threads INTEGER,
    total_messages INTEGER,
    brand_messages INTEGER,
    influencer_messages INTEGER,
    pending_proposals INTEGER,
    accepted_proposals INTEGER,
    pending_deliverables INTEGER,
    completed_deliverables INTEGER,
    unique_brand_users INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*)::INTEGER FROM chat.threads WHERE campaign_id = p_campaign_id),
        (SELECT COUNT(*)::INTEGER FROM chat.threads WHERE campaign_id = p_campaign_id AND is_active = TRUE),
        (SELECT COUNT(*)::INTEGER FROM chat.messages m JOIN chat.threads t ON m.thread_id = t.id WHERE t.campaign_id = p_campaign_id),
        (SELECT COUNT(*)::INTEGER FROM chat.messages m JOIN chat.threads t ON m.thread_id = t.id WHERE t.campaign_id = p_campaign_id AND m.sender_brand_id IS NOT NULL),
        (SELECT COUNT(*)::INTEGER FROM chat.messages m JOIN chat.threads t ON m.thread_id = t.id WHERE t.campaign_id = p_campaign_id AND m.sender_brand_id IS NULL),
        (SELECT COUNT(*)::INTEGER FROM chat.proposals WHERE campaign_id = p_campaign_id AND status = 'pending'),
        (SELECT COUNT(*)::INTEGER FROM chat.proposals WHERE campaign_id = p_campaign_id AND status = 'accepted'),
        (SELECT COUNT(*)::INTEGER FROM chat.deliverables WHERE campaign_id = p_campaign_id AND status IN ('pending', 'in_progress', 'submitted', 'under_review')),
        (SELECT COUNT(*)::INTEGER FROM chat.deliverables WHERE campaign_id = p_campaign_id AND status = 'approved'),
        (SELECT COUNT(DISTINCT sender_user_id)::INTEGER FROM chat.messages m JOIN chat.threads t ON m.thread_id = t.id WHERE t.campaign_id = p_campaign_id AND m.sender_brand_id IS NOT NULL);
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- ===========================================

-- Additional composite indexes for common query patterns
CREATE INDEX idx_messages_sender_thread ON chat.messages(sender_user_id, thread_id);
CREATE INDEX idx_messages_brand_thread ON chat.messages(sender_brand_id, thread_id);
CREATE INDEX idx_proposals_campaign_status ON chat.proposals(campaign_id, status);
CREATE INDEX idx_deliverables_campaign_status ON chat.deliverables(campaign_id, status);
CREATE INDEX idx_threads_brand_active ON chat.threads(brand_id, is_active) WHERE is_active = TRUE;
CREATE INDEX idx_threads_influencer_active ON chat.threads(influencer_user_id, is_active) WHERE is_active = TRUE;

-- Partial indexes for better performance
CREATE INDEX idx_messages_unseen ON chat.messages(thread_id, created_at) WHERE status != 'seen';
CREATE INDEX idx_proposals_pending ON chat.proposals(campaign_id, created_at DESC) WHERE status = 'pending';
CREATE INDEX idx_deliverables_overdue ON chat.deliverables(campaign_id, due_date) WHERE due_date < CURRENT_DATE AND status IN ('pending', 'in_progress');

-- ===========================================
-- SAMPLE DATA INSERTION FUNCTIONS (Optional)
-- ===========================================

-- Function to create a chat thread when influencer is added to campaign
CREATE OR REPLACE FUNCTION create_chat_thread_for_campaign_influencer()
RETURNS TRIGGER AS $$
DECLARE
    v_brand_id UUID;
    thread_title TEXT;
BEGIN
    -- Get brand ID from campaign
    SELECT c.brand_id INTO v_brand_id
    FROM campaigns.campaigns c
    WHERE c.id = NEW.campaign_id;

    -- Create thread title
    SELECT CONCAT('Campaign: ', c.name) INTO thread_title
    FROM campaigns.campaigns c
    WHERE c.id = NEW.campaign_id;

    -- Create chat thread
    INSERT INTO chat.threads (
        campaign_id,
        brand_id,
        influencer_user_id,
        campaign_influencer_id,
        thread_title
    ) VALUES (
        NEW.campaign_id,
        v_brand_id,
        NEW.influencer_user_id,
        NEW.id,
        thread_title
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically create chat thread when influencer is added to campaign
CREATE TRIGGER create_thread_on_campaign_influencer_insert
AFTER INSERT ON campaigns.campaign_influencers
FOR EACH ROW
WHEN (NEW.status = 'INVITED')
EXECUTE FUNCTION create_chat_thread_for_campaign_influencer();

-- ===========================================
-- ADDITIONAL HELPER FUNCTIONS FOR BRAND USER ACCESS
-- ===========================================

-- Function to check if user has access to brand
CREATE OR REPLACE FUNCTION user_has_brand_access(
    p_user_id UUID,
    p_brand_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM chat.brand_user_access
        WHERE user_id = p_user_id
        AND brand_id = p_brand_id
        AND is_active = TRUE
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get user's accessible brands
CREATE OR REPLACE FUNCTION get_user_accessible_brands(p_user_id UUID)
RETURNS TABLE (
    brand_id UUID,
    brand_name VARCHAR(100),
    organization_id UUID,
    access_level VARCHAR(20),
    can_create_campaigns BOOLEAN,
    can_manage_chat BOOLEAN,
    can_send_proposals BOOLEAN,
    can_approve_deliverables BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        bua.brand_id,
        b.name,
        b.organization_id,
        bua.access_level,
        bua.can_create_campaigns,
        bua.can_manage_chat,
        bua.can_send_proposals,
        bua.can_approve_deliverables
    FROM
        chat.brand_user_access bua
    JOIN
        users.brands b ON bua.brand_id = b.id
    WHERE
        bua.user_id = p_user_id
        AND bua.is_active = TRUE
        AND b.is_active = TRUE
    ORDER BY
        b.name;
END;
$$ LANGUAGE plpgsql;

-- Function to get brand team members for a campaign
CREATE OR REPLACE FUNCTION get_campaign_brand_team(p_campaign_id UUID)
RETURNS TABLE (
    user_id UUID,
    user_name VARCHAR(255),
    user_email VARCHAR(255),
    access_level VARCHAR(20),
    last_message_at TIMESTAMPTZ,
    message_count INTEGER
) AS $$
DECLARE
    v_brand_id UUID;
BEGIN
    -- Get brand ID from campaign
    SELECT brand_id INTO v_brand_id
    FROM campaigns.campaigns
    WHERE id = p_campaign_id;

    RETURN QUERY
    SELECT
        u.id,
        u.name,
        u.email,
        bua.access_level,
        MAX(m.created_at) AS last_message_at,
        COUNT(m.id)::INTEGER AS message_count
    FROM
        chat.brand_user_access bua
    JOIN
        users.users u ON bua.user_id = u.id
    LEFT JOIN
        chat.messages m ON m.sender_user_id = u.id
        AND m.sender_brand_id = v_brand_id
        AND m.thread_id IN (
            SELECT id FROM chat.threads WHERE campaign_id = p_campaign_id
        )
    WHERE
        bua.brand_id = v_brand_id
        AND bua.is_active = TRUE
        AND u.is_active = TRUE
    GROUP BY
        u.id, u.name, u.email, bua.access_level
    ORDER BY
        last_message_at DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql;
