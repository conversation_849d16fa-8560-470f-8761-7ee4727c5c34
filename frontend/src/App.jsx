import React, { useState, useEffect } from 'react';
import { connect, sendMessage } from './websocket';
import './App.css';

function App() {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [userId, setUserId] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState('Disconnected');

  useEffect(() => {
    if (userId) {
      console.log('Connecting to WebSocket...');
      const socket = connect(userId);

      // Add message handler
      socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        setMessages(prev => [...prev, data]);
      };

      socket.onopen = () => {
        setIsConnected(true);
        setConnectionState('Connected');
      };

      socket.onclose = () => {
        setIsConnected(false);
        setConnectionState('Disconnected');
      };

      socket.onerror = () => {
        setIsConnected(false);
        setConnectionState('Error');
      };

      return () => {
        if (socket) {
          socket.close();
        }
      };
    }
  }, [userId]);

  const handleSendMessage = () => {
    if (message.trim() && isConnected) {
      const messageData = {
        user: userId,
        message: message,
        timestamp: new Date().toISOString()
      };

      // Add message to local state immediately (for sender)
      setMessages(prev => [...prev, messageData]);

      // Send to other users
      sendMessage(messageData);
      setMessage('');
    }
  };

  const onKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  const handleUserSelect = (selectedUserId) => {
    setUserId(selectedUserId);
    setMessages([]); // Clear messages when switching users
  };

  if (!userId) {
    return (
      <div className="user-selection">
        <h2>Select User</h2>
        <button onClick={() => handleUserSelect('User1')} className="user-btn">
          User 1
        </button>
        <button onClick={() => handleUserSelect('User2')} className="user-btn">
          User 2
        </button>
      </div>
    );
  }

  return (
    <div className="chat-container">
      <div className="chat-header">
        <h3>Chat - {userId}</h3>
        <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
          {connectionState}
        </div>
        <button onClick={() => setUserId('')} className="switch-user-btn">
          Switch User
        </button>
      </div>

      <div className="messages-container">
        {messages.map((msg, index) => (
          <div
            key={index}
            className={`message ${msg.user === userId ? 'own-message' : 'other-message'}`}
          >
            <div className="message-user">{msg.user}</div>
            <div className="message-text">{msg.message}</div>
            <div className="message-time">
              {new Date(msg.timestamp).toLocaleTimeString()}
            </div>
          </div>
        ))}
      </div>

      <div className="input-container">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={onKeyDown}
          placeholder="Type a message..."
          disabled={!isConnected}
          className="message-input"
        />
        <button
          onClick={handleSendMessage}
          disabled={!isConnected || !message.trim()}
          className="send-btn"
        >
          Send
        </button>
      </div>
    </div>
  );
}

export default App;