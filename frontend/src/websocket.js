let socket = null;

export const connect = (userId) => {
  // Close existing connection if any
  if (socket) {
    socket.close();
  }
  
  console.log(`Attempting to connect to: ws://192.168.0.185:8000/ws/${userId}`);
  socket = new WebSocket(`ws://192.168.0.185:8000/ws/${userId}`);
  
  socket.onopen = () => {
    console.log(`WebSocket Connected Successfully for ${userId}`);
  };

  socket.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('Received message:', message);
  };

  socket.onclose = (event) => {
    console.log(`WebSocket Disconnected for ${userId}:`, event.code, event.reason);
  };

  socket.onerror = (error) => {
    console.error(`WebSocket Error for ${userId}:`, error);
  };
  
  return socket;
};

export const sendMessage = (message) => {
  console.log('Socket state:', socket ? socket.readyState : 'null');
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify(message));
    console.log('Message sent:', message);
  } else {
    console.error('WebSocket is not connected. State:', socket ? socket.readyState : 'null');
  }
};

export const getConnectionState = () => {
  if (!socket) return 'No socket';
  switch (socket.readyState) {
    case WebSocket.CONNECTING: return 'Connecting';
    case WebSocket.OPEN: return 'Open';
    case WebSocket.CLOSING: return 'Closing';
    case WebSocket.CLOSED: return 'Closed';
    default: return 'Unknown';
  }
};