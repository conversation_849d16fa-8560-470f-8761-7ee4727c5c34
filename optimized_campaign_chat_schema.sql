
-- ===========================================
-- USERS TABLE (Brands and Influencers)
-- ===========================================
CREATE TABLE users (
    id UUID PRIMARY KEY,
    name VARCHAR(100),
    role VARCHAR(20) CHECK (role IN ('brand', 'influencer')),
    email VARCHAR(150) UNIQUE,
    password_hash TEXT,
    profile_pic_url TEXT,
    bio TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    last_seen_at TIMESTAMP,
    is_online BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- CAMPAIGNS TABLE (Created by Brand)
-- ===========================================
CREATE TABLE campaigns (
    id UUID PRIMARY KEY,
    brand_id UUID REFERENCES users(id),
    title TEXT,
    description TEXT,
    start_date DATE,
    end_date DATE,
    status VARCHAR(20) CHECK (status IN ('draft', 'active', 'completed', 'cancelled')),
    budget NUMERIC(12,2),
    currency VARCHAR(10),
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- CAMPAIGN PARTICIPANTS TABLE
-- ===========================================
CREATE TABLE campaign_participants (
    id UUID PRIMARY KEY,
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    influencer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) CHECK (status IN ('invited', 'onboarded', 'negotiating', 'in_progress', 'submitted', 'completed', 'declined')),
    invited_at TIMESTAMP,
    onboarded_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- ===========================================
-- CHAT THREADS TABLE (One thread per influencer-campaign)
-- ===========================================
CREATE TABLE chat_threads (
    id UUID PRIMARY KEY,
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    brand_id UUID REFERENCES users(id),
    influencer_id UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- CHAT MESSAGES TABLE
-- ===========================================
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY,
    thread_id UUID REFERENCES chat_threads(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id),
    message TEXT,
    message_type VARCHAR(20) CHECK (message_type IN ('text', 'image', 'file', 'system', 'proposal')),
    status VARCHAR(20) CHECK (status IN ('sent', 'delivered', 'seen')) DEFAULT 'sent',
    created_at TIMESTAMP DEFAULT now(),
    delivered_at TIMESTAMP,
    seen_at TIMESTAMP
);

-- ===========================================
-- PROPOSALS TABLE (Proposal messages)
-- ===========================================
CREATE TABLE proposals (
    id UUID PRIMARY KEY,
    message_id UUID REFERENCES chat_messages(id) ON DELETE CASCADE,
    thread_id UUID REFERENCES chat_threads(id),
    campaign_id UUID REFERENCES campaigns(id),
    proposed_by UUID REFERENCES users(id),
    deliverables JSONB,
    amount NUMERIC(10,2),
    currency VARCHAR(10),
    status VARCHAR(20) CHECK (status IN ('pending', 'accepted', 'declined', 'countered')),
    valid_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- DELIVERABLES TABLE (Actual content posting)
-- ===========================================
CREATE TABLE deliverables (
    id UUID PRIMARY KEY,
    proposal_id UUID REFERENCES proposals(id),
    participant_id UUID REFERENCES campaign_participants(id),
    type VARCHAR(30) CHECK (type IN ('story', 'reel', 'post', 'video', 'other')),
    platform VARCHAR(20),
    content_url TEXT,
    proof_image_url TEXT,
    submitted_at TIMESTAMP,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP,
    status VARCHAR(20) CHECK (status IN ('submitted', 'approved', 'rejected')) DEFAULT 'submitted'
);

-- ===========================================
-- NOTIFICATIONS TABLE (Push/Badge)
-- ===========================================
CREATE TABLE notifications (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    type VARCHAR(50) CHECK (type IN ('message', 'proposal', 'campaign_update', 'system')),
    content TEXT,
    related_campaign_id UUID REFERENCES campaigns(id),
    is_seen BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT now(),
    seen_at TIMESTAMP
);

-- ===========================================
-- PRESENCE TABLE (Tracks user online/offline)
-- ===========================================
CREATE TABLE user_presence (
    user_id UUID PRIMARY KEY REFERENCES users(id),
    is_online BOOLEAN DEFAULT FALSE,
    last_seen_at TIMESTAMP,
    socket_session_id TEXT, -- optional tracking key
    updated_at TIMESTAMP DEFAULT now()
);

-- ===========================================
-- BULK CHAT MESSAGE LOG (For brand → multiple influencers)
-- ===========================================
CREATE TABLE bulk_messages (
    id UUID PRIMARY KEY,
    campaign_id UUID REFERENCES campaigns(id),
    sender_id UUID REFERENCES users(id),
    message TEXT,
    target_status VARCHAR(20),
    message_type VARCHAR(20) CHECK (message_type IN ('text', 'system')),
    created_at TIMESTAMP DEFAULT now()
);

-- Link to log which influencers received this message (optional optimization)
CREATE TABLE bulk_message_recipients (
    id UUID PRIMARY KEY,
    bulk_message_id UUID REFERENCES bulk_messages(id),
    recipient_id UUID REFERENCES users(id),
    thread_id UUID REFERENCES chat_threads(id),
    individual_message_id UUID REFERENCES chat_messages(id) -- if split per recipient
);
